package transaction

import (
	"context"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	TxDocument      *mongo.Collection
	AppConfig       *config.AppConfig
	ExecutedTxCache gcache.Cache
}

func NewModel(config *config.AppConfig) (*Model, error) {
	executedTxCache := gcache.New(102400).
		LFU().
		Expiration(time.Minute * 2).
		Build()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	clientOptions := options.Client().
		ApplyURI(config.MongoAddr)

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("mongo connect error %v", err)
		return nil, err
	}

	return &Model{
		AppConfig:       config,
		ExecutedTxCache: executedTxCache,
		TxDocument:      client.Database(config.Database).Collection(config.Collection),
	}, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []model.Transaction) (*mongo.BulkWriteResult, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	var operations []mongo.WriteModel
	for _, v := range transactionList {
		update := bson.M{
			"$set": v,
		}

		operation := mongo.NewUpdateOneModel().
			SetUpsert(true).
			SetFilter(v.GetUniqueKeys()).
			SetUpdate(update).
			SetHint("n_1_h_1")

		operations = append(operations, operation)
	}

	if len(operations) == 0 {
		return nil, nil
	}

	opts := options.BulkWrite().SetOrdered(false)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := s.TxDocument.BulkWrite(ctx, operations, opts)
	if err != nil {
		log.Errorf("BulkWrite error: %v", err)
		return nil, err
	}

	return result, nil
}
