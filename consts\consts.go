package consts

const (
	TokenProtocolErc20                             int64 = 0
	TokenProtocolErc721                            int64 = 1
	TokenProtocolErc1155                           int64 = 2
	TokenProtocolBrc20                             int64 = 3 //bitcoin brc-20协议
	TokenProtocolOmni                              int64 = 4 //bitcoin omni协议
	TokenProtocolErc404                            int64 = 5
	TokenProtocolSolNoFungible                     int64 = 6  //solana nft NonFungible
	TokenProtocolRune                              int64 = 7  //bitcoin runes
	TokenProtocolRgbBihelix                        int64 = 8  //bitcoin rgb Bihelix
	TokenProtocolSolFungibleAsset                  int64 = 9  //solana ft 默认 FungibleAsset
	TokenProtocolSolFungible                       int64 = 10 //solana Fungible
	TokenProtocolSolNonFungibleEdition             int64 = 11 //solana NonFungibleEdition
	TokenProtocolSolProgrammableNonFungible        int64 = 12 //solana ProgrammableNonFungible
	TokenProtocolSolProgrammableNonFungibleEdition int64 = 13 //solana ProgrammableNonFungibleEdition
)
