# Task Document

## Title

a sui transaction saver

store layer: mongodb

### EVENT文件生成
protoc --go_out=. --go-grpc_out=. .\event.proto

### 编译

    make

# 部署

## k8s

```
//打包docker
make docker

//上传docker
make docker-push

//部署docker到k8s 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make apply
```

停止服务

```
//调整pod为0 即停止服务 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make stop
```

删除服务

```
//删除dp 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make delete-server
```
# attach

k8s_prod run curl-test --image=radial/busyboxplus:curl -i --tty --rm -n tp-bserver

## Desc

```
//创建存储入块表 prod
# use admin;
# db.runCommand( { enablesharding :"sui"});
# use sui;
# db.createCollection("transaction");

# use admin;
# db.runCommand( { shardcollection : "sui.transaction", key : {n: 1, h: 1}, unique: true } );
# use sui;
# db.transaction.createIndex({h: 1});
# db.transaction.createIndex({"l.o": 1, "l.c": 1, t: -1});
# db.transaction.createIndex({"l.o": 1, "l.g": 1, t: -1});
# db.transaction.createIndex({"l.o": 1, "l.c": 1, "l.g": 1, t: -1});
# db.transaction.getIndexes();
```