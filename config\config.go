package config

import (
	cp "github.com/tokenbankteam/tb_common/cache"
	dbp "github.com/tokenbankteam/tb_common/db"
)

type AppConfig struct {
	Profile              string   `default:"dev"`
	Monitor              bool     `default:"true"`
	MonitorAddrs         []string `default:"localhost:7272"`
	PprofAddrs           []string `default:"localhost:7271"`
	Addr                 string   `default:"0.0.0.0:8080"`
	PushGateway          string
	PushJobName          string
	DB                   dbp.DBConfig
	Collection           string
	Redis                cp.RedisConfig
	Logger               string `default:"conf/logger.xml"`
	Debug                bool
	MongoAddr            string `default:"mongodb://*************:27017"`
	Database             string
	BrokerList           []string
	TrxSaveTopic         string
	TrxSaveConsumerGroup string
	BlockChain           string
	BlockChainId         int64
}
