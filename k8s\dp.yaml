kind: Deployment
apiVersion: apps/v1
metadata:
  name: TP_TARGET-test
  namespace: tp-bserver
spec:
  replicas: 3
  selector:
    matchLabels:
      srv: TP_TARGET-test
  template:
    metadata:
      creationTimestamp: null
      labels:
        srv: TP_TARGET-test
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: srv
                      operator: In
                      values:
                        - TP_TARGET-test
                topologyKey: "kubernetes.io/hostname"
      imagePullSecrets:
        - name: tp-hk-registry
      containers:
      - name: TP_TARGET-test
        image: registry.cn-hongkong.aliyuncs.com/tokenpocket/TP_TARGET-test:TPIMAGE_VERSION
        imagePullPolicy: "Always"
        ports:
        - containerPort: 5574
        resources:
          limits:
            cpu: '2'
            memory: 1Gi
          requests:
            cpu: 0.5
            memory: 100Mi
        volumeMounts:
        - name: configmap-volume
          mountPath: /home/<USER>/TP_TARGET-test/config/config_prod.yml
          subPath: config_prod.yml
      volumes:
      - name: configmap-volume
        configMap:
          name: cm-TP_TARGET-test
          items:
          - key: config_prod.yml
            path: config_prod.yml
