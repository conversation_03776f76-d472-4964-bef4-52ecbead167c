package task

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/Shopify/sarama"
	"github.com/bluele/gcache"
	cluster "github.com/bsm/sarama-cluster"
	log "github.com/cihub/seelog"
	"github.com/decred/base58"
	"github.com/prometheus/client_golang/prometheus"

	"encoding/hex"
	"strings"

	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/metrics"
	"github.com/tokenbankteam/sui_savetrx_task/model"
	"github.com/tokenbankteam/sui_savetrx_task/service"
	"github.com/tokenbankteam/sui_savetrx_task/utils"
)

// BlockChainSyncTask handles the synchronization of blockchain data
type BlockChainSyncTask struct {
	*model.BaseCacheModel
	*model.BaseDatabaseModel
	TransactionService   *service.TransactionService
	AppConfig            *config.AppConfig
	AppContext           *service.AppContext
	Consumer             *cluster.Consumer
	ContractAddressCache gcache.Cache
}

// NewBlockChainSyncTask creates a new BlockChainSyncTask instance
func NewBlockChainSyncTask(taskManager *Manager) (*BlockChainSyncTask, error) {
	appContext := taskManager.AppContext
	appConfig := appContext.Config
	consumer := utils.CreateConsumer(appConfig.BrokerList, []string{appConfig.TrxSaveTopic}, appConfig.TrxSaveConsumerGroup)

	return &BlockChainSyncTask{
		BaseDatabaseModel:    taskManager.BaseDatabaseModel,
		AppConfig:            appConfig,
		TransactionService:   appContext.Services["transactionService"].(*service.TransactionService),
		Consumer:             consumer,
		ContractAddressCache: gcache.New(20480).LFU().Expiration(time.Hour * 24).Build(),
	}, nil
}

// Start begins the blockchain synchronization process
func (s *BlockChainSyncTask) Start() {
	defer s.Close()
	s.ready()
}

// Close shuts down the consumer
func (s *BlockChainSyncTask) Close() error {
	if err := s.Consumer.Close(); err != nil {
		log.Errorf("Failed to shut down access log consumer cleanly: %v", err)
	}
	return nil
}

// ready prepares the task for processing messages
func (s *BlockChainSyncTask) ready() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt, syscall.SIGTERM)

	go s.handleNotifications()
	s.consumeMessages(signals)
}

// handleNotifications processes consumer notifications
func (s *BlockChainSyncTask) handleNotifications() {
	for ntf := range s.Consumer.Notifications() {
		marshal, _ := json.Marshal(ntf)
		log.Infof("Rebalanced notifications: %v", string(marshal))
	}
}

// consumeMessages processes incoming messages from the consumer
func (s *BlockChainSyncTask) consumeMessages(signals chan os.Signal) {
	for {
		select {
		case msg, ok := <-s.Consumer.Messages():
			if ok && msg != nil {
				s.processMessage(msg)
			} else {
				log.Errorf("Read message error: %v", msg)
			}
		case <-signals:
			log.Info("Received signal, stopping...")
			return
		}
	}
}

// processMessage handles a single message from the consumer
func (s *BlockChainSyncTask) processMessage(msg *sarama.ConsumerMessage) {
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		if _, err := s.processTxMsg(msg); err == nil {
			metrics.WalletTokenTaskHealthCounter.With(s.getMetricsLabels()).Inc()
			s.Consumer.MarkOffset(msg, "")
			return
		} else {
			log.Errorf("Failed to process message:%v %v", string(msg.Value), err)
		}
		metrics.WalletTokenTaskErrorCounter.With(s.getMetricsLabels()).Inc()
		log.Infof("Process attempt %d failed, retrying...", i+1)
		time.Sleep(time.Millisecond * 30)
	}
	log.Errorf("Failed to process message after %d attempts", maxRetries)
}

// processTxMsg unmarshals and processes a transaction message
func (s *BlockChainSyncTask) processTxMsg(msg *sarama.ConsumerMessage) (interface{}, error) {
	trxMessageBody := model.SuiKafkaMsg{}
	if err := json.Unmarshal(msg.Value, &trxMessageBody); err != nil {
		log.Errorf("Unmarshal error: %v", err)
		return nil, err
	}
	return s.processTransaction(&trxMessageBody)
}

// processTransaction handles a single transaction event
func (s *BlockChainSyncTask) processTransaction(transactionEvent *model.SuiKafkaMsg) (interface{}, error) {
	if transactionEvent == nil {
		return nil, errors.New("transactionEvent is nil")
	}

	digest := transactionEvent.TransactionOutputs.Effects.V2.TransactionDigest
	// Convert base58 digest to bytes
	digestBytes := base58.Decode(digest)

	sender, _ := hex.DecodeString(strings.TrimPrefix(transactionEvent.Certificate[0].Data[0].IntentMessage.Value.V1.Sender, "0x"))

	gasPaymentObject, _ := hex.DecodeString(strings.TrimPrefix(transactionEvent.TransactionOutputs.Transaction[0].Data[0].IntentMessage.Value.V1.GasData.Payment[0][0].(string), "0x"))
	gasObjectOwner, _ := hex.DecodeString(strings.TrimPrefix(transactionEvent.TransactionOutputs.Transaction[0].Data[0].IntentMessage.Value.V1.GasData.Owner, "0x"))

	tx := model.Transaction{
		Hash:        digestBytes,
		BlockNumber: int32(transactionEvent.Certificate[0].AuthSignature.Checkpoint[1]),
		Timestamp:   transactionEvent.Timestamp,
		// TxKind      string `json:"tx_kind" bson:"k"` //tx type
		Sender: sender,

		GasPaymentObject: gasPaymentObject,
		GasObjectOwner:   gasObjectOwner,

		ComputationCost:         transactionEvent.TransactionOutputs.Effects.V2.GasUsed.ComputationCost,
		NonRefundableStorageFee: transactionEvent.TransactionOutputs.Effects.V2.GasUsed.NonRefundableStorageFee,
		StorageCost:             transactionEvent.TransactionOutputs.Effects.V2.GasUsed.StorageCost,
		StorageRebate:           transactionEvent.TransactionOutputs.Effects.V2.GasUsed.StorageRebate,
		GasBudget:               fmt.Sprintf("%d", transactionEvent.TransactionOutputs.Transaction[0].Data[0].IntentMessage.Value.V1.GasData.Budget),

		// //MemoList          [][]byte        `json:"memo_list,omitempty" bson:"me,omitempty"`
		Status: 1,
		// ErrorMessage      []byte          `json:"error_message" bson:"e,omitempty"`
		BalanceChangeList: []model.BalanceChange{},
	}

	kind := transactionEvent.Certificate[0].Data[0].IntentMessage.Value.V1.Kind

	tx.TxKind = kind.GetNonNilFieldNamesWithTags()

	if _, ok := transactionEvent.TransactionOutputs.Effects.V2.Status.(string); !ok {
		tx.Status = 0
		tx.ErrorMessage, _ = json.Marshal(transactionEvent.TransactionOutputs.Effects.V2.Status)
	}

	// Fill balance changes from transaction event
	for _, balanceChange := range transactionEvent.BalanceChanges {
		var owner []byte
		ownerType := 0 // Default to AddressOwner
		if balanceChange.Owner.AddressOwner != "" {
			owner, _ = hex.DecodeString(strings.TrimPrefix(balanceChange.Owner.AddressOwner, "0x"))
		} else if balanceChange.Owner.ObjectOwner != "" {
			owner, _ = hex.DecodeString(strings.TrimPrefix(balanceChange.Owner.ObjectOwner, "0x"))
			ownerType = 1 // ObjectOwner
		} else if balanceChange.Owner.Shared.InitialSharedVersion != 0 {
			owner = []byte(strconv.Itoa(balanceChange.Owner.Shared.InitialSharedVersion))
			ownerType = 2 // SharedOwner
		}

		// Extract contract address from coin type
		// Example: 0x2::coin::Coin<0x9c86d1926a0a39e906f20674d6a35f337be8625ebcb6b799ee8ff011f328bee2::liq::LIQ>
		contractStart := strings.Index(balanceChange.CoinType, "<") + 1
		contractEnd := strings.Index(balanceChange.CoinType[contractStart:], "::") + contractStart
		var contract []byte
		if contractStart > 0 && contractEnd > contractStart {
			contractHex := balanceChange.CoinType[contractStart:contractEnd]
			if len(contractHex) == 0 {
				panic(fmt.Sprintf("contractHex is empty %v", balanceChange.CoinType))
			}
			if contractHex != "0x2" {
				contract, _ = hex.DecodeString(strings.TrimPrefix(contractHex, "0x"))
			} else {
				contract = []byte{}
			}
		}

		// Determine change type based on amount sign
		changeType := 0 // Default to incoming (positive)
		if strings.HasPrefix(balanceChange.Amount, "-") {
			changeType = 1 // Outgoing (negative)
			balanceChange.Amount = strings.TrimPrefix(balanceChange.Amount, "-")
		}

		tx.BalanceChangeList = append(tx.BalanceChangeList, model.BalanceChange{
			Owner:      owner,
			OwnerType:  ownerType,
			CoinType:   balanceChange.CoinType,
			Contract:   contract,
			ChangeType: changeType,
			Amount:     balanceChange.Amount,
		})
	}
	return s.TransactionService.InsertOrUpdateTransactionList([]model.Transaction{tx})
}

// getMetricsLabels returns prometheus labels for metrics
func (s *BlockChainSyncTask) getMetricsLabels() prometheus.Labels {
	return map[string]string{
		"blockchain_id": fmt.Sprintf("%v", s.AppConfig.BlockChainId),
		"blockchain":    s.AppConfig.BlockChain,
	}
}
