package main

import (
	"fmt"
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/sui_savetrx_task/model/transaction"
	"github.com/tokenbankteam/tb_common/metrics"
	"os"
	"time"

	"flag"
	"github.com/jinzhu/configor"
	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/service"
	"github.com/tokenbankteam/sui_savetrx_task/task"
	"github.com/tokenbankteam/tb_common/health"
	"github.com/tokenbankteam/tb_common/perf"
	_ "go.uber.org/automaxprocs"
)

var (
	version = ""
)

func main() {
	showVersion := flag.Bool("version", false, "Show version")
	configFlag := flag.String("config", "config/config.yml", "configuration file")
	flag.Parse()

	// Show version
	if *showVersion {
		fmt.Println(version)
		os.Exit(0)
	}

	var err error
	var appConfig config.AppConfig
	if err = configor.Load(&appConfig, *configFlag); err != nil {
		log.Criticalf("load config error: %v", err)
		os.Exit(1)
	}

	var logger log.LoggerInterface
	logger, err = log.LoggerFromConfigAsFile(appConfig.Logger)
	if err != nil {
		log.Errorf("init logger from %s error: %v", appConfig.Logger, err)
	} else {
		log.ReplaceLogger(logger)
	}
	defer log.Flush()
	log.Infof("Started Application at %v", time.Now().Format("January 2, 2006 at 3:04pm (MST)"))
	log.Infof("Version: %v", version)

	// start prof
	perf.Init(appConfig.PprofAddrs)
	if appConfig.Monitor {
		health.InitMonitor(appConfig.MonitorAddrs)
	}

	if appConfig.Profile == "prod" {
		metrics.Init(appConfig.PushGateway, appConfig.PushJobName)
	}

	transactionModel, err := transaction.NewModel(&appConfig)
	if err != nil {
		log.Errorf("init transactionModel error, %v", err)
		return
	}
	appContext := &service.AppContext{
		Config:   &appConfig,
		Services: map[string]interface{}{},
		Models: map[string]interface{}{
			"transactionModel": transactionModel,
		},
	}

	appContext.Services["transactionService"], _ = service.NewTransactionService(appContext)

	//启动定时任务管理器
	taskManager, err := task.NewTaskManager(&appConfig, appContext)
	if err != nil {
		log.Errorf("new task basePlugin error, %v", err)
		return
	}
	taskManager.Start()

	log.Info("Shutdown Server ...")
}
