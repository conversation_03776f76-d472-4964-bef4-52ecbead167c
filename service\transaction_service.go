package service

import (
	"time"

	log "github.com/cihub/seelog"
	"github.com/decred/base58"
	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/model"
	"github.com/tokenbankteam/sui_savetrx_task/model/transaction"
)

type TransactionService struct {
	AppConfig        *config.AppConfig
	TransactionModel *transaction.Model
}

func NewTransactionService(context *AppContext) (*TransactionService, error) {
	transactionService := &TransactionService{
		AppConfig:        context.Config,
		TransactionModel: context.Models["transactionModel"].(*transaction.Model),
	}
	return transactionService, nil
}

func (s *TransactionService) InsertOrUpdateTransactionList(transactionList []model.Transaction) (interface{}, error) {
	start := time.Now()
	defer func() {
		elapsed := time.Since(start).Milliseconds()
		if elapsed > 100 {
			log.Infof("InsertOrUpdateTransactionList %s, elapsed %vms", base58.Encode(transactionList[0].Hash), elapsed)
		}
	}()
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.TransactionModel.InsertOrUpdateTransactionList(transactionList)
}
