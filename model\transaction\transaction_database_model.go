package transaction

import (
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/model"
	"github.com/tokenbankteam/tb_common/db"
)

type DatabaseModel struct {
	model.BaseDatabaseModel
	config *config.AppConfig
}

func NewDatabaseModel(config *config.AppConfig) (*DatabaseModel, error) {
	dbs, err := db.GetDatabases(&config.DB)
	if err != nil {
		log.Errorf("get databases error: %v", err)
		return nil, err
	}
	return &DatabaseModel{config: config, BaseDatabaseModel: model.BaseDatabaseModel{
		Config: config,
		DBs:    dbs,
	}}, nil
}
