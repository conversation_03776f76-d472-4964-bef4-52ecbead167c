FROM alpine:3.15

ARG TARGET
ARG WORKPATH=/home/<USER>

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && apk del tzdata

RUN addgroup -g 500 tokenbank \
&& adduser -u 500 -D -h ${WORKPATH} -G tokenbank tokenbank
USER tokenbank

RUN mkdir -p ${WORKPATH}/${TARGET}

WORKDIR ${WORKPATH}/${TARGET}

COPY ${TARGET} ./server

RUN mkdir config
COPY ./config/logger.xml ./config

ENTRYPOINT ["./server", "-config", "config/config_prod.yml"]