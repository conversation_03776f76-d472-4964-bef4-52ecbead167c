package utils

import (
	"crypto/tls"
	"github.com/Shopify/sarama"
	cluster "github.com/bsm/sarama-cluster"
	log "github.com/cihub/seelog"
	"time"
)

func CreateConsumer(brokerList []string, topicList []string, groupId string) *cluster.Consumer {
	kafkaConfig := cluster.NewConfig()
	kafkaConfig.Group.Return.Notifications = true
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest
	consumer, err := cluster.NewConsumer(brokerList, groupId, topicList, kafkaConfig)
	if err != nil {
		log.Errorf("new consumer %v %v %v error %v", brokerList, groupId, topicList, err)
		return nil
	}
	return consumer
}

func createTlsConfiguration() (t *tls.Config) {
	return t
}

func CreateProducer(brokerList []string) *sarama.AsyncProducer {
	// For the access log, we are looking for AP semantics, with high throughput.
	// By creating batches of compressed messages, we reduce network I/O at a cost of more latency.
	config1 := sarama.NewConfig()
	tlsConfig := createTlsConfiguration()
	if tlsConfig != nil {
		config1.Net.TLS.Enable = true
		config1.Net.TLS.Config = tlsConfig
	}
	config1.Producer.RequiredAcks = sarama.WaitForLocal       // Only wait for the leader to ack
	config1.Producer.Compression = sarama.CompressionSnappy   // Compress messages
	config1.Producer.Flush.Frequency = 500 * time.Millisecond // Flush batches every 500ms

	producer, err := sarama.NewAsyncProducer(brokerList, config1)
	if err != nil {
		log.Errorf("Failed to start Sarama producer: %v", err)
	}

	// We will just log to STDOUT if we're not able to produce messages.
	// Note: messages will only be returned here after all retry attempts are exhausted.
	go func() {
		if producer != nil {
			for err := range producer.Errors() {
				log.Errorf("Failed to write access log entry: %v", err)
			}
		}
	}()
	return &producer
}
