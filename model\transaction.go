package model

import (
	"go.mongodb.org/mongo-driver/bson"
)

type Transaction struct {
	Hash        []byte `json:"hash" bson:"h"`         // tx digest
	BlockNumber int32  `json:"block_number" bson:"n"` // checkpoint seq num
	Timestamp   int64  `json:"timestamp,omitempty" bson:"t"`
	TxKind      string `json:"tx_kind" bson:"k"` //tx type
	Sender      []byte `json:"sender" bson:"sd"`

	GasPaymentObject []byte `json:"gas_payment_object" bson:"gp"`
	GasObjectOwner   []byte `json:"gas_object_owner" bson:"go"` //FeePayer

	ComputationCost         string `json:"computationCost" bson:"cc"`
	NonRefundableStorageFee string `json:"nonRefundableStorageFee" bson:"nr"`
	StorageCost             string `json:"storageCost" bson:"sc"`
	StorageRebate           string `json:"storageRebate" bson:"sr"`
	GasBudget               string `json:"gas_budget" bson:"gl"` //gas limit

	//MemoList          [][]byte        `json:"memo_list,omitempty" bson:"me,omitempty"`
	Status            int64           `json:"status" bson:"s"` //1 (success) or 0 (failure) or 2(pending), 99未知
	ErrorMessage      []byte          `json:"error_message" bson:"e,omitempty"`
	BalanceChangeList []BalanceChange `json:"balance_change_list" bson:"l,omitempty"`
}

type BalanceChange struct {
	Owner      []byte `json:"owner" bson:"o"`
	OwnerType  int    `json:"owner_type" bson:"ot"` // 0:AddressOwner or 1:ObjectOwner
	CoinType   string `json:"coin_type" bson:"ct"`  // eg: 0x2::coin::Coin<0x9c86d1926a0a39e906f20674d6a35f337be8625ebcb6b799ee8ff011f328bee2::liq::LIQ>
	Contract   []byte `json:"contract" bson:"c"`
	ChangeType int    `json:"change_type" bson:"g"` // 0 转入 1 转出
	Amount     string `json:"amount" bson:"a"`
}

func (s *Transaction) GetUniqueKeys() bson.M {
	return bson.M{"n": s.BlockNumber, "h": s.Hash}
}
