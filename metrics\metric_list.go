package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/tokenbankteam/tb_common/metrics"
)

var WalletTokenTaskErrorCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_error_count",
	Help: "count of error in save trx task.",
}, []string{"blockchain_id", "blockchain"})

func init() {
	metrics.DefaultRegistry.Register(WalletTokenTaskErrorCounter)
}

var WalletTokenTaskHealthCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_health_count",
	Help: "count of health in save trx task.",
}, []string{"blockchain_id", "blockchain"})

func init() {
	metrics.DefaultRegistry.Register(WalletTokenTaskHealthCounter)
}
