package task

import (
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/sui_savetrx_task/config"
	"github.com/tokenbankteam/sui_savetrx_task/model"
	"github.com/tokenbankteam/sui_savetrx_task/service"
	"github.com/tokenbankteam/tb_common/cache"
	"github.com/tokenbankteam/tb_common/db"
)

type Manager struct {
	*model.BaseCacheModel
	*model.BaseDatabaseModel

	AppContext *service.AppContext
	Config     *config.AppConfig

	blockChainSyncTask *BlockChainSyncTask
}

func NewTaskManager(config *config.AppConfig, appContext *service.AppContext) (*Manager, error) {
	manager := &Manager{
		AppContext: appContext,
		Config:     config,
	}
	return manager, nil
}

func (s *Manager) init() {
	caches, err := cache.GetCaches(&s.Config.Redis)
	if err != nil {
		log.Errorf("get caches error: %v", err)
		return
	}
	s.BaseCacheModel = &model.BaseCacheModel{
		Config: s.Config,
		Dbs:    caches,
	}

	databases, err := db.GetDatabases(&s.Config.DB)
	if err != nil {
		log.Errorf("get databases error: %v", err)
		return
	}
	s.BaseDatabaseModel = &model.BaseDatabaseModel{
		Config: s.Config,
		DBs:    databases,
	}
}

// 启动定时任务管理器
func (s *Manager) Start() {
	s.init()

	blockChainSyncTask, err := NewBlockChainSyncTask(s)
	if err != nil {
		log.Errorf("new blockChainSync task error, %v", err)
		return
	}
	s.blockChainSyncTask = blockChainSyncTask
	s.blockChainSyncTask.Start()
}
