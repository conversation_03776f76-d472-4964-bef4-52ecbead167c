# 系统环境
profile: prod

# 是否监控
monitor: true
# PprofAddrs *************:7272,********:7272
monitoraddrs: [ '0.0.0.0:12574' ]

# PprofAddrs *************:7271,********:7271
pprofaddrs: [ '0.0.0.0:12573' ]

# 服务监听地址
addr: '0.0.0.0:12575'

pushgateway: 'http://tppushgateway.mytokenpocket.vip'
pushjobname: 'task_sui_save_trx'

# 日志配置文件
logger: config/logger.xml

# 数据库配置
db:
  #实例列表
  instances:
    master:
      driver: mysql
      url: root:G6W9$NqTFxYy@tcp(*************:3306)/mc_transaction?timeout=3s&readTimeout=3s&writeTimeout=3s&parseTime=true
    lock_master:
      driver: mysql
      url: root:G6W9$NqTFxYy@tcp(*************:3306)/mc_transaction?timeout=3s&readTimeout=3s&writeTimeout=3s&parseTime=true

# 缓存配置
redis:
  #实例列表
  instances:
    master:
      url: server.redis.com:6379
      password: ""

debug: false
mongoaddr: 'mongodb://hkmgs1mginternal.mytokenpocket.vip:27017,hkmgs2mginternal.mytokenpocket.vip:27017,hkmgs3mginternal.mytokenpocket.vip:27017'
database: 'sui'
collection: 'transaction'

brokerlist: [ '0.kf-ttri1xazs6weg70s.automq-0b8t.automq.private:9092', '1.kf-ttri1xazs6weg70s.automq-0b8t.automq.private:9092', '2.kf-ttri1xazs6weg70s.automq-0b8t.automq.private:9092' ]
trxsavetopic: 'sui_trx_v2'
trxsaveconsumergroup: 'task_save_trx_sui'
blockchain: 'sui'
blockchainid: 48