package model

import (
	"go.mongodb.org/mongo-driver/bson"
)

type OwnerTokenOp int

const (
	OwnerTokenAddOp    OwnerTokenOp = 0
	OwnerTokenDeleteOp OwnerTokenOp = 1
)

type OwnerTokenUpdate struct {
	Owner     []byte       `json:"owner"`
	ProgramId []byte       `json:"program_id"`
	Contract  []byte       `json:"contract"`
	Ata       []byte       `json:"ata_list"` //associated token account list
	Op        OwnerTokenOp `json:"delete"`
	Slot      int32        `json:"s"`
}

type OwnerToken struct {
	BlockChainId    int64  `json:"blockchain_id" bson:"b"`
	OwnerAddress    []byte `json:"owner_address" bson:"o"`
	ContractAddress []byte `json:"contract_address" bson:"c"`
	Ts              int32  `json:"ts" bson:"t"`
	UpdateTime      int32  `json:"update_time" bson:"u"`
}

func (o *OwnerToken) GetUniqueKeys() bson.M {
	return bson.M{"o": o.Owner<PERSON>ddress, "c": o.<PERSON>tractAddress}
}
