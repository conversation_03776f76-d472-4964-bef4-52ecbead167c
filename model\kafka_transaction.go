package model

import (
	"reflect"
)

type SuiKafkaMsg struct {
	BalanceChanges []SuiBalanceChange `json:"balance_changes"`
	Certificate    []struct {
		AuthSignature struct {
			Checkpoint []int `json:"Checkpoint"`
		} `json:"auth_signature"`
		Data []struct {
			IntentMessage struct {
				Intent struct {
					AppId   int `json:"app_id"`
					Scope   int `json:"scope"`
					Version int `json:"version"`
				} `json:"intent"`
				Value struct {
					V1 struct {
						Expiration interface{} `json:"expiration"`
						GasData    struct {
							Budget  int             `json:"budget"`
							Owner   string          `json:"owner"`
							Payment [][]interface{} `json:"payment"`
							Price   int             `json:"price"`
						} `json:"gas_data"`
						Kind   SuiMsgKind `json:"kind"`
						Sender string     `json:"sender"`
					} `json:"V1"`
				} `json:"value"`
			} `json:"intent_message"`
			TxSignatures []string `json:"tx_signatures"`
		} `json:"data"`
	} `json:"certificate"`
	Timestamp          int64 `json:"timestamp"`
	TransactionOutputs struct {
		Deleted []interface{} `json:"deleted"`
		Effects struct {
			V2 struct {
				AuxDataDigest  interface{}     `json:"aux_data_digest"`
				ChangedObjects [][]interface{} `json:"changed_objects"`
				Dependencies   []string        `json:"dependencies"`
				EventsDigest   interface{}     `json:"events_digest"`
				ExecutedEpoch  int             `json:"executed_epoch"`
				GasObjectIndex int             `json:"gas_object_index"`
				GasUsed        struct {
					ComputationCost         string `json:"computationCost"`
					NonRefundableStorageFee string `json:"nonRefundableStorageFee"`
					StorageCost             string `json:"storageCost"`
					StorageRebate           string `json:"storageRebate"`
				} `json:"gas_used"`
				LamportVersion         int           `json:"lamport_version"`
				Status                 interface{}   `json:"status"`
				TransactionDigest      string        `json:"transaction_digest"`
				UnchangedSharedObjects []interface{} `json:"unchanged_shared_objects"`
			} `json:"V2"`
			V1 interface{} `json:"V1"`
		} `json:"effects"`
		Events struct {
			Data []interface{} `json:"data"`
		} `json:"events"`
		LocksToDelete  [][]interface{} `json:"locks_to_delete"`
		Markers        []interface{}   `json:"markers"`
		NewLocksToInit [][]interface{} `json:"new_locks_to_init"`
		Transaction    []struct {
			AuthSignature struct {
			} `json:"auth_signature"`
			Data []struct {
				IntentMessage struct {
					Intent struct {
						AppId   int `json:"app_id"`
						Scope   int `json:"scope"`
						Version int `json:"version"`
					} `json:"intent"`
					Value struct {
						V1 struct {
							Expiration interface{} `json:"expiration"`
							GasData    struct {
								Budget  int             `json:"budget"`
								Owner   string          `json:"owner"`
								Payment [][]interface{} `json:"payment"`
								Price   int             `json:"price"`
							} `json:"gas_data"`
							Kind   SuiMsgKind `json:"kind"`
							Sender string     `json:"sender"`
						} `json:"V1"`
					} `json:"value"`
				} `json:"intent_message"`
				TxSignatures []string `json:"tx_signatures"`
			} `json:"data"`
		} `json:"transaction"`
		Wrapped []interface{}        `json:"wrapped"`
		Written map[string]SuiObject `json:"written"`
	} `json:"transaction_outputs"`
}

type SuiBalanceChange struct {
	Amount   string `json:"amount"`
	CoinType string `json:"coinType"`
	Owner    struct {
		AddressOwner string `json:"AddressOwner"`
		ObjectOwner  string `json:"ObjectOwner"`
		Shared       struct {
			InitialSharedVersion int `json:"initial_shared_version"`
		} `json:"Shared"`
	} `json:"owner"`
}

type SuiTransactionStatus struct {
	Failure struct {
		Command interface{} `json:"command"`
		Error   interface{} `json:"error"`
	} `json:"Failure"`
}

type SuiMsgKind struct {
	ProgrammableTransaction *struct {
		Commands []struct {
			MoveCall struct {
				Arguments     interface{}   `json:"arguments"`
				Function      string        `json:"function"`
				Module        string        `json:"module"`
				Package       string        `json:"package"`
				TypeArguments []interface{} `json:"type_arguments"`
			} `json:"MoveCall"`
		} `json:"commands"`
		Inputs []struct {
			Object struct {
				SharedObject struct {
					Id                   string `json:"id"`
					InitialSharedVersion int    `json:"initial_shared_version"`
					Mutable              bool   `json:"mutable"`
				} `json:"SharedObject"`
			} `json:"Object,omitempty"`
			Pure []int `json:"Pure,omitempty"`
		} `json:"inputs"`
	} `json:"ProgrammableTransaction,omitempty" name:"ProgrammableTransaction"`
	ChangeEpoch               *interface{}  `json:"ChangeEpoch,omitempty" name:"ChangeEpoch"`
	Genesis                   *interface{}  `json:"Genesis,omitempty" name:"Genesis"`
	ConsensusCommitPrologue   *interface{}  `json:"ConsensusCommitPrologue,omitempty" name:"ConsensusCommitPrologue"`
	AuthenticatorStateUpdate  *interface{}  `json:"AuthenticatorStateUpdate,omitempty" name:"AuthenticatorStateUpdate"`
	EndOfEpochTransaction     []interface{} `json:"EndOfEpochTransaction,omitempty" name:"EndOfEpochTransaction"`
	RandomnessStateUpdate     *interface{}  `json:"RandomnessStateUpdate,omitempty" name:"RandomnessStateUpdate"`
	ConsensusCommitPrologueV2 *interface{}  `json:"ConsensusCommitPrologueV2,omitempty" name:"ConsensusCommitPrologueV2"`
	ConsensusCommitPrologueV3 *interface{}  `json:"ConsensusCommitPrologueV3,omitempty" name:"ConsensusCommitPrologueV3"`
}

func (s *SuiMsgKind) GetNonNilFieldNamesWithTags() string {
	val := reflect.ValueOf(s)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return ""
	}

	typ := val.Type()

	// 遍历所有字段
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 获取name标签
		nameTag := fieldType.Tag.Get("name")
		if nameTag == "" {
			continue
		}

		// 检查字段是否为nil
		switch field.Kind() {
		case reflect.Ptr:
			if !field.IsNil() {
				return nameTag
			}
		case reflect.Slice:
			if !field.IsNil() && field.Len() > 0 {
				return nameTag
			}
		}
	}
	return ""
}

//func (s *SuiMsgKind) GetNonNilFieldNamesWithTags() string {
//	val := reflect.ValueOf(s)
//	if val.Kind() == reflect.Ptr {
//		val = val.Elem()
//	}
//
//	typ := val.Type()
//
//	for i := 0; i < val.NumField(); i++ {
//		field := val.Field(i)
//		fieldType := typ.Field(i)
//
//		if (field.Kind() == reflect.Ptr || field.Kind() == reflect.Slice) && !field.IsNil() {
//			// 获取JSON标签
//			jsonTag := fieldType.Tag.Get("name")
//			return jsonTag
//		}
//	}
//
//	return ""
//}

type SuiObject struct {
	Data struct {
		Move struct {
			Contents          []int       `json:"contents"`
			HasPublicTransfer bool        `json:"has_public_transfer"`
			Type              interface{} `json:"type_"`
			Version           int         `json:"version"`
		} `json:"Move"`
		Package interface{} `json:"package"`
	} `json:"data"`
	Owner               interface{} `json:"owner"`
	PreviousTransaction string      `json:"previous_transaction"`
	StorageRebate       int         `json:"storage_rebate"`
}
