module github.com/tokenbankteam/sui_savetrx_task

go 1.20

require (
	github.com/Shopify/sarama v1.18.0
	github.com/bluele/gcache v0.0.2
	github.com/bsm/sarama-cluster v2.1.15+incompatible
	github.com/cihub/seelog v0.0.0-**************-f561c5e57575
	github.com/decred/base58 v1.0.5
	github.com/jinzhu/configor v1.0.0
	github.com/prometheus/client_golang v1.12.1
	github.com/tokenbankteam/tb_common v0.2.0
	go.mongodb.org/mongo-driver v1.12.2
	go.uber.org/automaxprocs v1.5.2
)

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/Shopify/toxiproxy v1.2.1 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/decred/dcrd/crypto/blake256 v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.4.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-**************-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/frankban/quicktest v1.14.6 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/go-redis/redis v6.13.0+incompatible // indirect
	github.com/go-sql-driver/mysql v1.4.0 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/golang/snappy v0.0.5-0.**************-fa5810519dcb // indirect
	github.com/google/go-cmp v0.5.9 // indirect
	github.com/klauspost/compress v1.15.15 // indirect
	github.com/lib/pq v0.0.0-20180523175426-90697d60dd84 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/montanaflynn/stats v0.0.0-20171201202039-1bf9dbcd8cbe // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/prometheus/client_model v0.2.1-0.20210607210712-147c58e9608a // indirect
	github.com/prometheus/common v0.32.1 // indirect
	github.com/prometheus/procfs v0.7.3 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/stretchr/testify v1.8.1 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20181117223130-1be2e3e5546d // indirect
	golang.org/x/crypto v0.12.0 // indirect
	golang.org/x/sync v0.3.0 // indirect
	golang.org/x/sys v0.11.0 // indirect
	golang.org/x/text v0.12.0 // indirect
	google.golang.org/appengine v1.6.6 // indirect
	google.golang.org/protobuf v1.27.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
